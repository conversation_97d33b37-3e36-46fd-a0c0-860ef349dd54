# VPS Deployment Guide for Odoo 14.0

This guide provides step-by-step instructions for deploying Odoo 14.0 on a VPS using Docker containers.

## 🖥️ VPS Requirements

### Minimum Specifications
- **CPU**: 2 cores
- **RAM**: 4GB (8GB recommended)
- **Storage**: 50GB SSD
- **OS**: Ubuntu 20.04/22.04 LTS or CentOS 8+
- **Network**: Public IP with ports 80, 443 accessible

### Recommended Specifications
- **CPU**: 4+ cores
- **RAM**: 8GB+ 
- **Storage**: 100GB+ SSD
- **Bandwidth**: Unmetered or high limit

## 🚀 Step-by-Step Deployment

### Step 1: Server Setup

```bash
# Connect to your VPS
ssh root@your-vps-ip

# Update system packages
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git unzip htop nano ufw

# Create deployment user
adduser odoo
usermod -aG sudo odoo
su - odoo
```

### Step 2: Install Docker

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version

# Logout and login to apply group changes
exit
ssh odoo@your-vps-ip
```

### Step 3: Upload Project Files

```bash
# Create deployment directory
mkdir -p /home/<USER>/odoo-deployment
cd /home/<USER>/odoo-deployment

# Upload files (from your local machine)
scp -r /path/to/your/project/* odoo@your-vps-ip:/home/<USER>/odoo-deployment/

# Or clone from repository
git clone https://your-repo.git .
```

### Step 4: Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

**Important .env settings for production:**

```bash
# Database Configuration
DB_NAME=odoo_production
DB_USER=odooprod
DB_PASSWORD=your_very_secure_password_here

# Odoo Configuration  
ADMIN_PASSWORD=your_admin_master_password
ODOO_PORT=8069

# Domain Configuration
DOMAIN=your-domain.com
EMAIL=<EMAIL>

# Performance (adjust based on your VPS specs)
WORKERS=3
MEMORY_HARD=2415919104
MEMORY_SOFT=2013265920
```

### Step 5: Configure Firewall

```bash
# Enable UFW firewall
sudo ufw enable

# Allow SSH (important!)
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Check firewall status
sudo ufw status
```

### Step 6: Deploy Application

```bash
# Make scripts executable
chmod +x *.sh

# Deploy in production mode
./deploy.sh production

# Check deployment status
./manage.sh status
```

### Step 7: Configure Domain and SSL

#### Option A: Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Stop nginx container temporarily
docker-compose stop nginx

# Generate SSL certificate
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates to nginx directory
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
sudo chown odoo:odoo nginx/ssl/*

# Restart nginx
docker-compose start nginx
```

#### Option B: Using Custom SSL Certificates

```bash
# Copy your certificates
cp your-certificate.pem nginx/ssl/cert.pem
cp your-private-key.pem nginx/ssl/key.pem

# Restart nginx
docker-compose restart nginx
```

### Step 8: Verify Deployment

```bash
# Check all services are running
./manage.sh status

# Check logs for any errors
./manage.sh logs

# Test web access
curl -I https://your-domain.com
```

## 🔧 Post-Deployment Configuration

### 1. Initial Odoo Setup

1. Access your Odoo instance: `https://your-domain.com`
2. Create your first database
3. Install required modules
4. Configure company settings

### 2. Setup Automated Backups

```bash
# Create backup script in crontab
crontab -e

# Add daily backup at 2 AM
0 2 * * * /home/<USER>/odoo-deployment/backup.sh

# Add weekly cleanup
0 3 * * 0 find /home/<USER>/odoo-deployment/backups -name "*.tar.gz" -mtime +30 -delete
```

### 3. Configure Log Rotation

```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/odoo-docker

# Add configuration
/home/<USER>/odoo-deployment/nginx/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 odoo odoo
    postrotate
        docker-compose -f /home/<USER>/odoo-deployment/docker-compose.yml restart nginx
    endscript
}
```

### 4. Setup Monitoring

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Create monitoring script
nano monitor.sh
```

```bash
#!/bin/bash
echo "=== System Resources ==="
free -h
df -h
echo "=== Docker Stats ==="
docker stats --no-stream
echo "=== Service Status ==="
cd /home/<USER>/odoo-deployment && ./manage.sh status
```

## 🔒 Security Hardening

### 1. SSH Security

```bash
# Edit SSH configuration
sudo nano /etc/ssh/sshd_config

# Recommended settings:
Port 2222                    # Change default port
PermitRootLogin no          # Disable root login
PasswordAuthentication no   # Use key-based auth only
MaxAuthTries 3             # Limit auth attempts

# Restart SSH
sudo systemctl restart ssh
```

### 2. Install Fail2Ban

```bash
# Install fail2ban
sudo apt install -y fail2ban

# Configure for SSH
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = 2222
```

```bash
# Start fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 3. Regular Updates

```bash
# Create update script
nano update.sh
```

```bash
#!/bin/bash
# System updates
sudo apt update && sudo apt upgrade -y

# Docker updates
cd /home/<USER>/odoo-deployment
./manage.sh upgrade

# Cleanup
docker system prune -f
```

## 📊 Monitoring and Maintenance

### Daily Checks

```bash
# Check service status
./manage.sh status

# Check disk space
df -h

# Check memory usage
free -h

# Check recent logs
./manage.sh logs --tail 50
```

### Weekly Maintenance

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean Docker resources
docker system prune -f

# Check backup integrity
ls -la backups/

# Review security logs
sudo journalctl -u ssh -n 100
```

### Monthly Tasks

```bash
# Renew SSL certificates (if using Let's Encrypt)
sudo certbot renew

# Database maintenance
./manage.sh db-shell
# In PostgreSQL: VACUUM ANALYZE;

# Review and rotate logs
sudo logrotate -f /etc/logrotate.d/odoo-docker
```

## 🐛 Troubleshooting Common Issues

### Issue 1: Services Won't Start

```bash
# Check Docker daemon
sudo systemctl status docker

# Check logs
./manage.sh logs

# Check disk space
df -h

# Restart services
./manage.sh restart
```

### Issue 2: SSL Certificate Issues

```bash
# Check certificate validity
openssl x509 -in nginx/ssl/cert.pem -text -noout

# Regenerate self-signed certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem

# Restart nginx
docker-compose restart nginx
```

### Issue 3: Database Connection Problems

```bash
# Check database logs
./manage.sh logs db

# Restart database
docker-compose restart db

# Check database connectivity
./manage.sh db-shell
```

### Issue 4: Performance Issues

```bash
# Check resource usage
htop
docker stats

# Adjust worker count in .env
nano .env
# Increase WORKERS value

# Restart Odoo
docker-compose restart odoo
```

## 📞 Getting Help

If you encounter issues:

1. Check the logs: `./manage.sh logs`
2. Review this guide and README.md
3. Check Docker and Odoo documentation
4. Verify your VPS meets minimum requirements
5. Ensure all ports are properly configured

## ✅ Deployment Checklist

- [ ] VPS meets minimum requirements
- [ ] Docker and Docker Compose installed
- [ ] Project files uploaded and configured
- [ ] Environment variables set correctly
- [ ] Firewall configured properly
- [ ] Domain DNS pointing to VPS
- [ ] SSL certificates configured
- [ ] Services running and accessible
- [ ] Automated backups configured
- [ ] Monitoring and logging set up
- [ ] Security hardening applied
- [ ] Documentation reviewed and understood

Congratulations! Your Odoo 14.0 instance should now be running successfully on your VPS.
