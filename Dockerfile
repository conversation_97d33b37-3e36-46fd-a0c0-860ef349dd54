FROM python:3.8-slim-bullseye

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libxml2-dev \
    libxslt1-dev \
    libldap2-dev \
    libsasl2-dev \
    libtiff5-dev \
    libjpeg62-turbo-dev \
    libopenjp2-7-dev \
    zlib1g-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    libpq-dev \
    postgresql-client \
    wkhtmltopdf \
    xfonts-75dpi \
    xfonts-base \
    fontconfig \
    && rm -rf /var/lib/apt/lists/*

# Create odoo user
RUN useradd -ms /bin/bash odoo

# Set working directory
WORKDIR /opt/odoo

# Copy and extract Odoo core
COPY odoo-14.0-core.tar.gz /tmp/
RUN tar -xzf /tmp/odoo-14.0-core.tar.gz -C /opt/odoo/ \
    && rm /tmp/odoo-14.0-core.tar.gz

# Copy and extract custom addons
COPY platform-odex25-light.tar.gz /tmp/
RUN tar -xzf /tmp/platform-odex25-light.tar.gz -C /opt/odoo/ \
    && rm /tmp/platform-odex25-light.tar.gz

# Copy and extract data directory
COPY prod_data_dir.tar.gz /tmp/
RUN tar -xzf /tmp/prod_data_dir.tar.gz -C /opt/odoo/ \
    && rm /tmp/prod_data_dir.tar.gz

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip
COPY requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir -r /opt/odoo/odoo-14.0/requirements.txt
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Copy configuration file
COPY odoo.conf /etc/odoo/odoo.conf

# Create necessary directories and set permissions
RUN mkdir -p /var/lib/odoo /var/log/odoo /etc/odoo \
    && chown -R odoo:odoo /opt/odoo /var/lib/odoo /var/log/odoo /etc/odoo

# Switch to odoo user
USER odoo

# Expose ports
EXPOSE 8069 8072

# Set the default command
CMD ["/opt/odoo/odoo-14.0/odoo-bin", "-c", "/etc/odoo/odoo.conf"]
