#!/bin/bash

# Odoo Management Script
# Usage: ./manage.sh [command] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Show usage
show_usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  status          Show service status"
    echo "  logs [service]  Show logs (optionally for specific service)"
    echo "  shell           Access Odoo container shell"
    echo "  db-shell        Access database shell"
    echo "  backup          Create database backup"
    echo "  restore [file]  Restore database from backup"
    echo "  update          Update Odoo modules"
    echo "  install [module] Install Odoo module"
    echo "  upgrade         Upgrade Docker images"
    echo "  clean           Clean up unused Docker resources"
    echo ""
}

# Start services
start_services() {
    print_status "Starting Odoo services..."
    docker-compose up -d
    print_status "Services started."
}

# Stop services
stop_services() {
    print_status "Stopping Odoo services..."
    docker-compose down
    print_status "Services stopped."
}

# Restart services
restart_services() {
    print_status "Restarting Odoo services..."
    docker-compose restart
    print_status "Services restarted."
}

# Show service status
show_status() {
    print_status "Service status:"
    docker-compose ps
}

# Show logs
show_logs() {
    local service=${1:-}
    if [ -n "$service" ]; then
        print_status "Showing logs for $service..."
        docker-compose logs -f "$service"
    else
        print_status "Showing logs for all services..."
        docker-compose logs -f
    fi
}

# Access Odoo shell
odoo_shell() {
    print_status "Accessing Odoo container shell..."
    docker-compose exec odoo bash
}

# Access database shell
db_shell() {
    print_status "Accessing database shell..."
    docker-compose exec db psql -U odooprod -d odoo
}

# Create database backup
create_backup() {
    local backup_file="backups/odoo_backup_$(date +%Y%m%d_%H%M%S).sql"
    print_status "Creating database backup: $backup_file"
    
    docker-compose exec db pg_dump -U odooprod -d odoo > "$backup_file"
    
    if [ $? -eq 0 ]; then
        print_status "Backup created successfully: $backup_file"
    else
        print_error "Backup failed!"
        exit 1
    fi
}

# Restore database from backup
restore_backup() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        print_error "Please specify backup file to restore."
        echo "Usage: $0 restore [backup_file]"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        print_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    print_warning "This will overwrite the current database. Are you sure? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled."
        exit 0
    fi
    
    print_status "Restoring database from: $backup_file"
    
    # Stop Odoo service
    docker-compose stop odoo
    
    # Drop and recreate database
    docker-compose exec db dropdb -U odooprod odoo
    docker-compose exec db createdb -U odooprod odoo
    
    # Restore backup
    docker-compose exec -T db psql -U odooprod -d odoo < "$backup_file"
    
    # Start Odoo service
    docker-compose start odoo
    
    print_status "Database restored successfully."
}

# Update Odoo modules
update_modules() {
    print_status "Updating Odoo modules..."
    docker-compose exec odoo /opt/odoo/odoo-14.0/odoo-bin -c /etc/odoo/odoo.conf -u all --stop-after-init
    print_status "Modules updated."
}

# Install Odoo module
install_module() {
    local module=$1
    
    if [ -z "$module" ]; then
        print_error "Please specify module name to install."
        echo "Usage: $0 install [module_name]"
        exit 1
    fi
    
    print_status "Installing module: $module"
    docker-compose exec odoo /opt/odoo/odoo-14.0/odoo-bin -c /etc/odoo/odoo.conf -i "$module" --stop-after-init
    print_status "Module installed: $module"
}

# Upgrade Docker images
upgrade_images() {
    print_status "Upgrading Docker images..."
    docker-compose pull
    docker-compose build --no-cache
    docker-compose up -d
    print_status "Images upgraded."
}

# Clean up Docker resources
clean_docker() {
    print_status "Cleaning up unused Docker resources..."
    docker system prune -f
    docker volume prune -f
    print_status "Cleanup completed."
}

# Main function
main() {
    local command=$1
    shift || true
    
    case "$command" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$@"
            ;;
        shell)
            odoo_shell
            ;;
        db-shell)
            db_shell
            ;;
        backup)
            create_backup
            ;;
        restore)
            restore_backup "$@"
            ;;
        update)
            update_modules
            ;;
        install)
            install_module "$@"
            ;;
        upgrade)
            upgrade_images
            ;;
        clean)
            clean_docker
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
