version: '3.8'

services:
  db:
    image: postgres:13
    container_name: odoo_db
    environment:
      POSTGRES_DB: ${DB_NAME:-odoo}
      POSTGRES_USER: ${DB_USER:-odooprod}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-odooprod}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - odoo_db_data:/var/lib/postgresql/data/pgdata
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - odoo_network

  odoo:
    build: .
    container_name: odoo_app
    depends_on:
      - db
    environment:
      - HOST=${DB_HOST:-db}
      - USER=${DB_USER:-odooprod}
      - PASSWORD=${DB_PASSWORD:-odooprod}
    volumes:
      - odoo_web_data:/var/lib/odoo
      - odoo_logs:/var/log/odoo
      - ./config:/etc/odoo
      - ./addons:/opt/odoo/custom-addons
      - ./backups:/backups
    ports:
      - "${ODOO_PORT:-8069}:8069"
      - "${ODOO_LONGPOLLING_PORT:-8072}:8072"
    restart: unless-stopped
    networks:
      - odoo_network

  nginx:
    image: nginx:alpine
    container_name: odoo_nginx
    depends_on:
      - odoo
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    restart: unless-stopped
    networks:
      - odoo_network

volumes:
  odoo_web_data:
  odoo_db_data:
  odoo_logs:

networks:
  odoo_network:
    driver: bridge
