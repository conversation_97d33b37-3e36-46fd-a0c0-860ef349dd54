#!/bin/bash

# Odoo Backup Script
# Creates full backup including database, filestore, and configuration

set -e

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Configuration
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="odoo_full_backup_${DATE}"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"
    print_status "Created backup directory: ${BACKUP_DIR}/${BACKUP_NAME}"
}

# Backup database
backup_database() {
    print_status "Backing up database..."
    
    docker-compose exec -T db pg_dump -U ${DB_USER:-odooprod} -d ${DB_NAME:-odoo} \
        > "${BACKUP_DIR}/${BACKUP_NAME}/database.sql"
    
    if [ $? -eq 0 ]; then
        print_status "Database backup completed."
    else
        print_error "Database backup failed!"
        exit 1
    fi
}

# Backup filestore
backup_filestore() {
    print_status "Backing up filestore..."
    
    docker-compose exec -T odoo tar -czf - /var/lib/odoo \
        > "${BACKUP_DIR}/${BACKUP_NAME}/filestore.tar.gz"
    
    if [ $? -eq 0 ]; then
        print_status "Filestore backup completed."
    else
        print_error "Filestore backup failed!"
        exit 1
    fi
}

# Backup configuration
backup_config() {
    print_status "Backing up configuration..."
    
    # Copy configuration files
    cp -r config "${BACKUP_DIR}/${BACKUP_NAME}/" 2>/dev/null || true
    cp .env "${BACKUP_DIR}/${BACKUP_NAME}/" 2>/dev/null || true
    cp docker-compose.yml "${BACKUP_DIR}/${BACKUP_NAME}/"
    cp nginx/nginx.conf "${BACKUP_DIR}/${BACKUP_NAME}/" 2>/dev/null || true
    
    print_status "Configuration backup completed."
}

# Create backup metadata
create_metadata() {
    print_status "Creating backup metadata..."
    
    cat > "${BACKUP_DIR}/${BACKUP_NAME}/backup_info.txt" << EOF
Backup Information
==================
Date: $(date)
Backup Name: ${BACKUP_NAME}
Database: ${DB_NAME:-odoo}
Database User: ${DB_USER:-odooprod}
Odoo Version: 14.0
Docker Images:
$(docker-compose images)

Files Included:
- database.sql (PostgreSQL dump)
- filestore.tar.gz (Odoo filestore)
- Configuration files
EOF

    print_status "Backup metadata created."
}

# Compress backup
compress_backup() {
    print_status "Compressing backup..."
    
    cd "${BACKUP_DIR}"
    tar -czf "${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}"
    rm -rf "${BACKUP_NAME}"
    
    print_status "Backup compressed: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
}

# Clean old backups
clean_old_backups() {
    print_status "Cleaning old backups (older than ${RETENTION_DAYS} days)..."
    
    find "${BACKUP_DIR}" -name "odoo_full_backup_*.tar.gz" -mtime +${RETENTION_DAYS} -delete
    
    print_status "Old backups cleaned."
}

# Upload to remote storage (optional)
upload_backup() {
    if [ -n "${BACKUP_REMOTE_PATH}" ]; then
        print_status "Uploading backup to remote storage..."
        
        # Example for rsync (customize as needed)
        # rsync -avz "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" "${BACKUP_REMOTE_PATH}/"
        
        # Example for AWS S3 (uncomment and configure)
        # aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" "s3://${S3_BUCKET}/backups/"
        
        print_status "Backup uploaded to remote storage."
    fi
}

# Main backup function
main() {
    print_status "Starting Odoo backup process..."
    
    # Check if services are running
    if ! docker-compose ps | grep -q "Up"; then
        print_error "Odoo services are not running. Please start them first."
        exit 1
    fi
    
    create_backup_dir
    backup_database
    backup_filestore
    backup_config
    create_metadata
    compress_backup
    clean_old_backups
    upload_backup
    
    print_status "Backup process completed successfully!"
    print_status "Backup file: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    
    # Show backup size
    BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" | cut -f1)
    print_status "Backup size: ${BACKUP_SIZE}"
}

# Run main function
main "$@"
