#!/bin/bash

# Odoo Docker Deployment Script
# Usage: ./deploy.sh [production|development]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if Docker and Docker Compose are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Dependencies check passed."
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p nginx/ssl nginx/logs
    mkdir -p backups
    mkdir -p config
    mkdir -p addons
    
    print_status "Directories created."
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certs() {
    print_status "Generating SSL certificates..."
    
    if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/key.pem" ]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        
        print_status "SSL certificates generated."
    else
        print_status "SSL certificates already exist."
    fi
}

# Setup environment file
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before running the application."
    else
        print_status "Environment file already exists."
    fi
}

# Build and start services
deploy_services() {
    local env_type=${1:-development}
    
    print_header "Deploying Odoo in $env_type mode"
    
    # Build the application
    print_status "Building Docker images..."
    docker-compose build --no-cache
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service status
    print_status "Checking service status..."
    docker-compose ps
}

# Main deployment function
main() {
    local env_type=${1:-development}
    
    print_header "Odoo Docker Deployment"
    
    check_dependencies
    create_directories
    generate_ssl_certs
    setup_environment
    deploy_services $env_type
    
    print_header "Deployment Complete"
    print_status "Odoo is now running at:"
    print_status "  HTTP:  http://localhost"
    print_status "  HTTPS: https://localhost"
    print_status ""
    print_status "Useful commands:"
    print_status "  View logs:     docker-compose logs -f"
    print_status "  Stop services: docker-compose down"
    print_status "  Restart:       docker-compose restart"
    print_status "  Shell access:  docker-compose exec odoo bash"
}

# Run main function
main "$@"
