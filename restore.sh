#!/bin/bash

# Odoo Restore Script
# Restores full backup including database, filestore, and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
show_usage() {
    echo "Usage: $0 [backup_file.tar.gz]"
    echo ""
    echo "Example:"
    echo "  $0 backups/odoo_full_backup_20231201_120000.tar.gz"
    echo ""
    echo "Available backups:"
    ls -la backups/odoo_full_backup_*.tar.gz 2>/dev/null || echo "  No backups found"
}

# Validate backup file
validate_backup() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        print_error "Please specify backup file to restore."
        show_usage
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        print_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    print_status "Backup file validated: $backup_file"
}

# Extract backup
extract_backup() {
    local backup_file=$1
    local temp_dir="/tmp/odoo_restore_$(date +%s)"
    
    print_status "Extracting backup to temporary directory..."
    mkdir -p "$temp_dir"
    tar -xzf "$backup_file" -C "$temp_dir"
    
    # Find the extracted directory
    EXTRACTED_DIR=$(find "$temp_dir" -maxdepth 1 -type d -name "odoo_full_backup_*" | head -1)
    
    if [ -z "$EXTRACTED_DIR" ]; then
        print_error "Invalid backup file structure."
        rm -rf "$temp_dir"
        exit 1
    fi
    
    print_status "Backup extracted to: $EXTRACTED_DIR"
    echo "$EXTRACTED_DIR"
}

# Confirm restore
confirm_restore() {
    print_warning "This will overwrite the current Odoo installation and database."
    print_warning "Make sure you have a current backup before proceeding."
    echo ""
    print_warning "Are you sure you want to continue? (y/N)"
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled."
        exit 0
    fi
}

# Stop services
stop_services() {
    print_status "Stopping Odoo services..."
    docker-compose down
}

# Restore database
restore_database() {
    local extracted_dir=$1
    
    print_status "Restoring database..."
    
    # Start only database service
    docker-compose up -d db
    sleep 10
    
    # Load environment variables
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Drop and recreate database
    docker-compose exec db dropdb -U ${DB_USER:-odooprod} ${DB_NAME:-odoo} 2>/dev/null || true
    docker-compose exec db createdb -U ${DB_USER:-odooprod} ${DB_NAME:-odoo}
    
    # Restore database
    docker-compose exec -T db psql -U ${DB_USER:-odooprod} -d ${DB_NAME:-odoo} < "${extracted_dir}/database.sql"
    
    print_status "Database restored successfully."
}

# Restore filestore
restore_filestore() {
    local extracted_dir=$1
    
    print_status "Restoring filestore..."
    
    # Start Odoo service temporarily to restore filestore
    docker-compose up -d odoo
    sleep 10
    
    # Remove existing filestore and restore from backup
    docker-compose exec odoo rm -rf /var/lib/odoo/*
    docker-compose exec -T odoo tar -xzf - -C / < "${extracted_dir}/filestore.tar.gz"
    
    print_status "Filestore restored successfully."
}

# Restore configuration
restore_configuration() {
    local extracted_dir=$1
    
    print_status "Restoring configuration..."
    
    # Backup current configuration
    if [ -d "config" ]; then
        mv config "config.backup.$(date +%s)"
    fi
    
    # Restore configuration files
    if [ -d "${extracted_dir}/config" ]; then
        cp -r "${extracted_dir}/config" ./
    fi
    
    if [ -f "${extracted_dir}/.env" ]; then
        cp "${extracted_dir}/.env" ./
    fi
    
    if [ -f "${extracted_dir}/nginx.conf" ]; then
        mkdir -p nginx
        cp "${extracted_dir}/nginx.conf" nginx/
    fi
    
    print_status "Configuration restored successfully."
}

# Start services
start_services() {
    print_status "Starting all services..."
    docker-compose up -d
    
    # Wait for services to be ready
    sleep 30
    
    print_status "Services started successfully."
}

# Cleanup
cleanup() {
    local temp_dir=$1
    
    if [ -n "$temp_dir" ] && [ -d "$temp_dir" ]; then
        print_status "Cleaning up temporary files..."
        rm -rf "$temp_dir"
    fi
}

# Show restore information
show_restore_info() {
    local extracted_dir=$1
    
    if [ -f "${extracted_dir}/backup_info.txt" ]; then
        print_status "Restore completed. Backup information:"
        echo ""
        cat "${extracted_dir}/backup_info.txt"
    fi
}

# Main restore function
main() {
    local backup_file=$1
    
    print_status "Starting Odoo restore process..."
    
    validate_backup "$backup_file"
    confirm_restore
    
    # Extract backup
    extracted_dir=$(extract_backup "$backup_file")
    
    # Perform restore
    stop_services
    restore_database "$extracted_dir"
    restore_filestore "$extracted_dir"
    restore_configuration "$extracted_dir"
    start_services
    
    # Show information and cleanup
    show_restore_info "$extracted_dir"
    cleanup "$(dirname "$extracted_dir")"
    
    print_status "Restore process completed successfully!"
    print_status "Odoo is now running with the restored data."
    print_status "Access URL: https://localhost"
}

# Run main function
main "$@"
