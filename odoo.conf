[options]
admin_passwd = ${ADMIN_PASSWORD:-5XdF*CVsdb2$3%dsei5I$}
db_host = ${DB_HOST:-db}
db_port = ${DB_PORT:-5432}
db_user = ${DB_USER:-odooprod}
db_password = ${DB_PASSWORD:-odooprod}
db_name = ${DB_NAME:-odoo}

addons_path = /opt/odoo/odoo-14.0/addons,/opt/odoo/odoo-14.0/odoo/addons,/opt/odoo/platform-odex25-light/master/odex25_accounting,/opt/odoo/platform-odex25-light/master/odex25_bi,/opt/odoo/platform-odex25-light/master/odex25_fleet,/opt/odoo/platform-odex25-light/master/odex25_hr,/opt/odoo/platform-odex25-light/master/odex25_project,/opt/odoo/platform-odex25-light/master/odex25_realstate,/opt/odoo/platform-odex25-light/master/odex25_transactions,/opt/odoo/platform-odex25-light/master/odex25_base,/opt/odoo/platform-odex25-light/master/odex25_dms,/opt/odoo/platform-odex25-light/master/odex25_helpdesk,/opt/odoo/platform-odex25-light/master/odex25_maintenance,/opt/odoo/platform-odex25-light/master/odex25_purchase,/opt/odoo/platform-odex25-light/master/odex25_sales,/opt/odoo/platform-odex25-light/master/odex25_website,/opt/odoo/platform-odex25-light/master/openeducat_erp,/opt/odoo/custom-addons

xmlrpc_port = 8069
longpolling_port = 8072
xmlrpc_interface = 0.0.0.0
netrpc_interface = 0.0.0.0
proxy_mode = True

data_dir = /var/lib/odoo
logfile = /var/log/odoo/odoo.log
log_level = info

# Performance settings
limit_memory_hard = ${MEMORY_HARD:-2415919104}
limit_memory_soft = ${MEMORY_SOFT:-2013265920}
limit_request = ${LIMIT_REQUEST:-8192}
limit_time_cpu = ${LIMIT_TIME_CPU:-600}
limit_time_real = ${LIMIT_TIME_REAL:-1200}
workers = ${WORKERS:-3}
max_cron_threads = ${MAX_CRON_THREADS:-1}

# Database settings
db_maxconn = ${DB_MAXCONN:-64}

# Security
list_db = False
