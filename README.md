# Odoo 14.0 Docker Deployment

This repository contains a complete Docker-based deployment solution for Odoo 14.0 with custom addons (odex25-light platform) designed for VPS deployment.

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM
- 20GB+ disk space

### Initial Setup

1. **Clone and prepare the environment:**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit configuration (important!)
   nano .env
   ```

2. **Deploy the application:**
   ```bash
   # Make scripts executable
   chmod +x *.sh
   
   # Deploy in production mode
   ./deploy.sh production
   ```

3. **Access your Odoo instance:**
   - HTTP: `http://your-domain.com`
   - HTTPS: `https://your-domain.com`

## 📁 Project Structure

```
├── Dockerfile                 # Odoo application container
├── docker-compose.yml        # Multi-container orchestration
├── odoo.conf                 # Odoo configuration template
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── deploy.sh                # Main deployment script
├── manage.sh                # Management utilities
├── backup.sh                # Backup script
├── restore.sh               # Restore script
├── nginx/
│   ├── nginx.conf           # Nginx reverse proxy config
│   └── ssl/                 # SSL certificates
├── config/                  # Configuration files
├── addons/                  # Custom addons directory
└── backups/                 # Backup storage
```

## 🔧 Configuration

### Environment Variables (.env)

Key variables to configure:

```bash
# Database
DB_NAME=odoo
DB_USER=odooprod
DB_PASSWORD=your_secure_password

# Odoo
ADMIN_PASSWORD=your_admin_password
ODOO_PORT=8069

# Domain (for production)
DOMAIN=your-domain.com
EMAIL=<EMAIL>

# Performance
WORKERS=3
MEMORY_HARD=2415919104
MEMORY_SOFT=2013265920
```

### SSL Certificates

For production, replace self-signed certificates:

```bash
# Copy your certificates
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem

# Or use Let's Encrypt (recommended)
# See SSL_SETUP.md for detailed instructions
```

## 🛠️ Management Commands

### Using manage.sh

```bash
# Service management
./manage.sh start           # Start all services
./manage.sh stop            # Stop all services
./manage.sh restart         # Restart all services
./manage.sh status          # Show service status

# Monitoring
./manage.sh logs            # Show all logs
./manage.sh logs odoo       # Show Odoo logs only
./manage.sh logs db         # Show database logs

# Access
./manage.sh shell           # Access Odoo container
./manage.sh db-shell        # Access database shell

# Maintenance
./manage.sh backup          # Create full backup
./manage.sh restore backup.tar.gz  # Restore from backup
./manage.sh update          # Update Odoo modules
./manage.sh install module_name     # Install module
```

## 💾 Backup & Restore

### Automated Backups

```bash
# Create full backup
./backup.sh

# Schedule daily backups (add to crontab)
0 2 * * * /path/to/your/project/backup.sh
```

### Manual Restore

```bash
# List available backups
ls -la backups/

# Restore from backup
./restore.sh backups/odoo_full_backup_20231201_120000.tar.gz
```

## 🌐 VPS Deployment Guide

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Deploy Application

```bash
# Upload project files to VPS
scp -r . user@your-vps:/opt/odoo-deployment/

# SSH to VPS and deploy
ssh user@your-vps
cd /opt/odoo-deployment
./deploy.sh production
```

### 3. Configure Firewall

```bash
# Allow HTTP/HTTPS traffic
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## 🔒 Security Considerations

### Production Checklist

- [ ] Change default passwords in `.env`
- [ ] Use proper SSL certificates
- [ ] Configure firewall rules
- [ ] Set up regular backups
- [ ] Monitor logs regularly
- [ ] Keep Docker images updated
- [ ] Restrict database access
- [ ] Enable fail2ban for SSH

### Security Headers

The Nginx configuration includes:
- HSTS headers
- XSS protection
- Content type options
- Frame options
- Rate limiting

## 📊 Monitoring

### Health Checks

```bash
# Check service health
docker-compose ps

# Monitor resource usage
docker stats

# Check logs for errors
./manage.sh logs | grep -i error
```

### Performance Monitoring

- Monitor CPU and memory usage
- Check database performance
- Monitor disk space
- Set up log rotation

## 🔄 Updates & Maintenance

### Updating Odoo

```bash
# Pull latest images
docker-compose pull

# Rebuild and restart
./manage.sh upgrade

# Update modules
./manage.sh update
```

### Database Maintenance

```bash
# Access database for maintenance
./manage.sh db-shell

# Vacuum database (in psql)
VACUUM ANALYZE;
```

## 🐛 Troubleshooting

### Common Issues

1. **Services won't start:**
   ```bash
   # Check logs
   ./manage.sh logs
   
   # Check disk space
   df -h
   ```

2. **Database connection errors:**
   ```bash
   # Restart database
   docker-compose restart db
   
   # Check database logs
   ./manage.sh logs db
   ```

3. **SSL certificate issues:**
   ```bash
   # Regenerate self-signed certificates
   ./deploy.sh
   ```

### Log Locations

- Odoo logs: `docker-compose logs odoo`
- Database logs: `docker-compose logs db`
- Nginx logs: `nginx/logs/`

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Docker and Odoo logs
3. Consult Odoo documentation
4. Check Docker Compose documentation

## 🏗️ Architecture

### Container Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │      Odoo       │    │   PostgreSQL    │
│  (Reverse Proxy)│◄──►│   Application   │◄──►│    Database     │
│   Port 80/443   │    │   Port 8069     │    │   Port 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Persistence

- **Database**: PostgreSQL data stored in Docker volume
- **Filestore**: Odoo attachments in Docker volume
- **Logs**: Persistent log storage
- **Configuration**: Mounted from host

## 🔧 Advanced Configuration

### Custom Addons

Place custom addons in the `addons/` directory:

```bash
# Add custom addon
cp -r my_custom_addon addons/
./manage.sh restart
./manage.sh install my_custom_addon
```

### Performance Tuning

Edit `.env` for your server specs:

```bash
# For 8GB RAM server
WORKERS=5
MEMORY_HARD=**********
MEMORY_SOFT=**********

# For 16GB RAM server
WORKERS=9
MEMORY_HARD=**********
MEMORY_SOFT=**********
```

### Database Optimization

```sql
-- Connect to database
./manage.sh db-shell

-- Optimize queries
ANALYZE;
REINDEX DATABASE odoo;

-- Check database size
SELECT pg_size_pretty(pg_database_size('odoo'));
```

## 📄 License

This deployment configuration is provided as-is for educational and production use.
